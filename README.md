# SS节点提取器 - Cloudflare Worker版本

这是原Python脚本的Cloudflare Worker JavaScript版本，用于获取和解密SS节点信息。

## 功能特性

- 🚀 基于Cloudflare Workers，全球边缘计算
- 🔐 AES-CBC解密支持
- 📱 RESTful API接口
- 🌐 CORS支持，可在浏览器中直接调用
- ⚡ 无服务器架构，按需执行

## 部署步骤

### 1. 安装依赖

```bash
npm install
```

### 2. 登录Cloudflare

```bash
npx wrangler login
```

### 3. 部署到Cloudflare Workers

```bash
# 部署到生产环境
npm run deploy:production

# 或部署到测试环境
npm run deploy:staging

# 本地开发
npm run dev
```

## API使用

部署成功后，访问Worker URL即可获取SS节点列表：

```bash
curl https://your-worker.your-subdomain.workers.dev/
```

### 响应格式

```json
{
  "success": true,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "count": 5,
  "links": [
    "ss://YWVzLTI1Ni1jZmI6cGFzc3dvcmRAaXA6cG9ydA==#节点名称1",
    "ss://YWVzLTI1Ni1jZmI6cGFzc3dvcmRAaXA6cG9ydA==#节点名称2"
  ]
}
```

## 主要改动

从Python脚本转换为Cloudflare Worker时的主要改动：

1. **加密库替换**: `pyaes` → Web Crypto API
2. **HTTP请求**: `requests` → `fetch` API
3. **Base64编码**: `base64` → `btoa()`
4. **JSON解析**: `json.loads()` → `JSON.parse()`
5. **错误处理**: 添加了完整的错误处理和CORS支持

## 注意事项

- Cloudflare Workers有执行时间限制（免费版10秒，付费版30秒）
- 请确保API密钥和加密参数的安全性
- 建议在生产环境中添加访问控制和速率限制

## 作者

原作者: 𝐼𝑢  
Worker版本转换: AI Assistant

## 许可证

MIT License
