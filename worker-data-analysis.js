// 数据分析版本 - 分析原始data参数
export default {
  async fetch(request) {
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });
    }

    try {
      // 工具函数
      function hexToUint8Array(hexString) {
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
          bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
        }
        return bytes;
      }

      async function aesDecrypt(encryptedData, key, iv) {
        const cryptoKey = await crypto.subtle.importKey(
          'raw', key, { name: 'AES-CBC' }, false, ['decrypt']
        );
        const decrypted = await crypto.subtle.decrypt(
          { name: 'AES-CBC', iv: iv }, cryptoKey, encryptedData
        );
        const decryptedArray = new Uint8Array(decrypted);
        if (decryptedArray.length === 0) return decryptedArray;
        const paddingLength = decryptedArray[decryptedArray.length - 1];
        if (paddingLength <= 16 && paddingLength <= decryptedArray.length) {
          return decryptedArray.slice(0, decryptedArray.length - paddingLength);
        }
        return decryptedArray;
      }

      async function aesEncrypt(data, key, iv) {
        const cryptoKey = await crypto.subtle.importKey(
          'raw', key, { name: 'AES-CBC' }, false, ['encrypt']
        );
        
        // 添加PKCS7填充
        const blockSize = 16;
        const paddingLength = blockSize - (data.length % blockSize);
        const paddedData = new Uint8Array(data.length + paddingLength);
        paddedData.set(data);
        for (let i = data.length; i < paddedData.length; i++) {
          paddedData[i] = paddingLength;
        }
        
        const encrypted = await crypto.subtle.encrypt(
          { name: 'AES-CBC', iv: iv }, cryptoKey, paddedData
        );
        return new Uint8Array(encrypted);
      }

      function uint8ArrayToHex(bytes) {
        return Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');
      }

      // 密钥和IV
      const key = new TextEncoder().encode('65151f8d966bf596');
      const iv = new TextEncoder().encode('88ca0f0ea1ecf975');

      // 分析原始data参数
      const originalData = '4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed';
      
      console.log('🔍 分析原始data参数...');
      
      let originalDecrypted = null;
      try {
        const originalEncryptedData = hexToUint8Array(originalData);
        const originalDecryptedData = await aesDecrypt(originalEncryptedData, key, iv);
        const originalDecryptedText = new TextDecoder().decode(originalDecryptedData);
        originalDecrypted = JSON.parse(originalDecryptedText);
        console.log('✅ 原始data解密成功:', originalDecrypted);
      } catch (error) {
        console.log('❌ 原始data解密失败:', error.message);
      }

      // 尝试生成新的请求数据
      const testPayloads = [
        { message: "空对象", data: {} },
        { message: "基本请求", data: { action: "getServerList" } },
        { message: "带用户ID", data: { userId: "test", action: "getServerList" } },
        { message: "带设备信息", data: { deviceId: "test-device", platform: "ios" } },
        { message: "模拟原始格式", data: { request_ip: "127.0.0.1", request_type: "1" } }
      ];

      const results = [];

      for (const payload of testPayloads) {
        try {
          console.log(`🧪 测试载荷: ${payload.message}`);
          
          // 加密新的载荷
          const jsonString = JSON.stringify(payload.data);
          const dataToEncrypt = new TextEncoder().encode(jsonString);
          const encryptedData = await aesEncrypt(dataToEncrypt, key, iv);
          const encryptedHex = uint8ArrayToHex(encryptedData);
          
          console.log(`📦 生成的加密数据: ${encryptedHex.substring(0, 50)}...`);
          
          // 发送请求
          const response = await fetch('http://api.skrapp.net/api/serverlist', {
            method: 'POST',
            headers: {
              'accept': '*/*',
              'accept-language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
              'appversion': '1.3.1',
              'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
              'content-type': 'application/x-www-form-urlencoded',
              'Cookie': 'PHPSESSID=fnffo1ivhvt0ouo6ebqn86a0d4'
            },
            body: `data=${encryptedHex}`
          });

          const responseText = await response.text();
          
          let responseDecrypted = null;
          let responseError = null;

          if (responseText.trim() && /^[0-9a-fA-F]+$/.test(responseText.trim())) {
            try {
              const responseEncryptedData = hexToUint8Array(responseText.trim());
              const responseDecryptedData = await aesDecrypt(responseEncryptedData, key, iv);
              const responseDecryptedText = new TextDecoder().decode(responseDecryptedData);
              responseDecrypted = JSON.parse(responseDecryptedText);
            } catch (decryptError) {
              responseError = `解密失败: ${decryptError.message}`;
            }
          } else {
            responseError = `无效响应: ${responseText.substring(0, 100)}`;
          }

          results.push({
            payload: payload,
            encryptedHex: encryptedHex.substring(0, 100) + '...',
            status: response.status,
            responseLength: responseText.length,
            responseDecrypted: responseDecrypted,
            responseError: responseError
          });

        } catch (testError) {
          results.push({
            payload: payload,
            error: `请求失败: ${testError.message}`
          });
        }
      }

      return new Response(JSON.stringify({
        success: true,
        message: "数据分析完成",
        originalDataAnalysis: {
          originalData: originalData,
          originalDecrypted: originalDecrypted
        },
        testResults: results,
        timestamp: new Date().toISOString()
      }, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });

    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }, null, 2), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};
