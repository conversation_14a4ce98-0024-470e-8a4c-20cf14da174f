// 改进版本的Cloudflare Worker - SS节点提取器
export default {
  async fetch(request) {
    // 支持CORS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });
    }

    try {
      console.log("🚀 开始SS节点提取...");
      
      // 工具函数
      function hexToUint8Array(hexString) {
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
          bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
        }
        return bytes;
      }

      function base64Encode(str) {
        const encoder = new TextEncoder();
        const data = encoder.encode(str);
        return btoa(String.fromCharCode(...data));
      }

      // AES-CBC解密函数
      async function aesDecrypt(encryptedData, key, iv) {
        const cryptoKey = await crypto.subtle.importKey(
          'raw', key, { name: 'AES-CBC' }, false, ['decrypt']
        );
        
        const decrypted = await crypto.subtle.decrypt(
          { name: 'AES-CBC', iv: iv }, cryptoKey, encryptedData
        );
        
        const decryptedArray = new Uint8Array(decrypted);
        if (decryptedArray.length === 0) return decryptedArray;
        
        // 移除PKCS7填充
        const paddingLength = decryptedArray[decryptedArray.length - 1];
        if (paddingLength <= 16 && paddingLength <= decryptedArray.length) {
          return decryptedArray.slice(0, decryptedArray.length - paddingLength);
        }
        return decryptedArray;
      }

      // API配置
      const apiUrl = 'http://api.skrapp.net/api/serverlist';
      const headers = {
        'accept': '*/*',
        'accept-language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
        'appversion': '1.3.1',
        'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
        'content-type': 'application/x-www-form-urlencoded',
        'Cookie': 'PHPSESSID=fnffo1ivhvt0ouo6ebqn86a0d4'
      };
      const postData = 'data=4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed';
      
      // 密钥和IV
      const key = new TextEncoder().encode('65151f8d966bf596');
      const iv = new TextEncoder().encode('88ca0f0ea1ecf975');

      // 发送API请求
      console.log("📡 发送API请求...");
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: postData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log("📥 API响应长度:", responseText.length);

      // 验证和解密
      if (!responseText.trim() || !/^[0-9a-fA-F]+$/.test(responseText.trim())) {
        throw new Error(`无效的API响应格式`);
      }

      const encryptedData = hexToUint8Array(responseText.trim());
      const decryptedData = await aesDecrypt(encryptedData, key, iv);
      const decryptedText = new TextDecoder().decode(decryptedData);
      
      console.log("🔓 解密成功，数据长度:", decryptedText.length);

      let jsonData;
      try {
        jsonData = JSON.parse(decryptedText);
      } catch (parseError) {
        throw new Error(`JSON解析失败: ${parseError.message}`);
      }

      // 处理不同类型的响应
      if (jsonData.request_ip && jsonData.request_type) {
        // 这是请求信息，不是服务器列表
        return new Response(JSON.stringify({
          success: false,
          error: 'API返回的是请求信息，可能需要不同的请求参数',
          info: '解密成功，但数据格式不是预期的服务器列表',
          decryptedData: jsonData,
          suggestion: '可能需要检查API参数或Cookie是否有效',
          timestamp: new Date().toISOString()
        }, null, 2), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }

      // 检查服务器列表格式
      if (!jsonData.data || !Array.isArray(jsonData.data)) {
        return new Response(JSON.stringify({
          success: false,
          error: '数据格式不符合预期',
          decryptedData: jsonData,
          timestamp: new Date().toISOString()
        }, null, 2), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }

      // 生成SS链接
      const ssLinks = [];
      for (const server of jsonData.data) {
        if (!server.password || !server.ip || !server.port || !server.title) {
          console.warn('⚠️ 跳过无效服务器数据:', server);
          continue;
        }
        
        const ssConfig = `aes-256-cfb:${server.password}@${server.ip}:${server.port}`;
        const encodedConfig = base64Encode(ssConfig);
        const ssLink = `ss://${encodedConfig}#${server.title}`;
        ssLinks.push(ssLink);
      }

      console.log("✅ 成功生成", ssLinks.length, "个SS链接");

      return new Response(JSON.stringify({
        success: true,
        timestamp: new Date().toISOString(),
        count: ssLinks.length,
        links: ssLinks,
        debug: {
          originalDataLength: responseText.length,
          decryptedDataLength: decryptedText.length,
          serversFound: jsonData.data.length
        }
      }, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });

    } catch (error) {
      console.error('❌ 错误:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }, null, 2), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};
