// 测试版本 - 专门用于调试解密过程
export default {
  async fetch(request, env, ctx) {
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });
    }

    try {
      // 十六进制字符串转Uint8Array
      function hexToUint8Array(hexString) {
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
          bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
        }
        return bytes;
      }

      // 测试用的已知加密数据（从API获取的示例）
      const testEncryptedHex = "4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed";
      
      // 密钥和IV (ASCII字符串，不是十六进制)
      const key = new TextEncoder().encode('65151f8d966bf596');
      const iv = new TextEncoder().encode('88ca0f0ea1ecf975');
      
      console.log('密钥:', Array.from(key).map(b => b.toString(16).padStart(2, '0')).join(''));
      console.log('IV:', Array.from(iv).map(b => b.toString(16).padStart(2, '0')).join(''));
      
      // 尝试解密测试数据
      const testEncryptedData = hexToUint8Array(testEncryptedHex);
      
      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        key,
        { name: 'AES-CBC' },
        false,
        ['decrypt']
      );
      
      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-CBC', iv: iv },
        cryptoKey,
        testEncryptedData
      );
      
      const decryptedArray = new Uint8Array(decrypted);
      const decryptedText = new TextDecoder().decode(decryptedArray);
      
      // 尝试移除填充
      let cleanText = decryptedText;
      if (decryptedArray.length > 0) {
        const paddingLength = decryptedArray[decryptedArray.length - 1];
        if (paddingLength <= 16 && paddingLength <= decryptedArray.length) {
          const cleanArray = decryptedArray.slice(0, decryptedArray.length - paddingLength);
          cleanText = new TextDecoder().decode(cleanArray);
        }
      }
      
      return new Response(JSON.stringify({
        success: true,
        debug: {
          keyHex: Array.from(key).map(b => b.toString(16).padStart(2, '0')).join(''),
          ivHex: Array.from(iv).map(b => b.toString(16).padStart(2, '0')).join(''),
          encryptedLength: testEncryptedData.length,
          decryptedLength: decryptedArray.length,
          decryptedRaw: Array.from(decryptedArray.slice(0, 50)).map(b => b.toString(16).padStart(2, '0')).join(''),
          decryptedText: decryptedText.substring(0, 200),
          cleanText: cleanText.substring(0, 200),
          lastBytes: Array.from(decryptedArray.slice(-10)).map(b => b.toString(16).padStart(2, '0')).join('')
        }
      }, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });

    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        stack: error.stack
      }, null, 2), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};
