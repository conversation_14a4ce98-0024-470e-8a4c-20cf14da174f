// 认证测试版本 - 尝试不同的认证方式
export default {
  async fetch(request) {
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });
    }

    try {
      // 工具函数
      function hexToUint8Array(hexString) {
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
          bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
        }
        return bytes;
      }

      async function aesDecrypt(encryptedData, key, iv) {
        const cryptoKey = await crypto.subtle.importKey(
          'raw', key, { name: 'AES-CBC' }, false, ['decrypt']
        );
        const decrypted = await crypto.subtle.decrypt(
          { name: 'AES-CBC', iv: iv }, cryptoKey, encryptedData
        );
        const decryptedArray = new Uint8Array(decrypted);
        if (decryptedArray.length === 0) return decryptedArray;
        const paddingLength = decryptedArray[decryptedArray.length - 1];
        if (paddingLength <= 16 && paddingLength <= decryptedArray.length) {
          return decryptedArray.slice(0, decryptedArray.length - paddingLength);
        }
        return decryptedArray;
      }

      // 密钥和IV
      const key = new TextEncoder().encode('65151f8d966bf596');
      const iv = new TextEncoder().encode('88ca0f0ea1ecf975');

      const apiUrl = 'http://api.skrapp.net/api/serverlist';
      
      // 尝试多种不同的认证方式
      const testConfigs = [
        {
          name: "原始配置",
          headers: {
            'accept': '*/*',
            'accept-language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
            'appversion': '1.3.1',
            'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
            'content-type': 'application/x-www-form-urlencoded',
            'Cookie': 'PHPSESSID=fnffo1ivhvt0ouo6ebqn86a0d4'
          },
          data: 'data=4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed'
        },
        {
          name: "无Cookie测试",
          headers: {
            'accept': '*/*',
            'accept-language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
            'appversion': '1.3.1',
            'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: 'data=4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed'
        },
        {
          name: "空数据测试",
          headers: {
            'accept': '*/*',
            'accept-language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
            'appversion': '1.3.1',
            'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
            'content-type': 'application/x-www-form-urlencoded',
            'Cookie': 'PHPSESSID=fnffo1ivhvt0ouo6ebqn86a0d4'
          },
          data: ''
        }
      ];

      const results = [];

      for (const config of testConfigs) {
        try {
          console.log(`🧪 测试配置: ${config.name}`);
          
          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: config.headers,
            body: config.data
          });

          const responseText = await response.text();
          console.log(`📥 ${config.name} 响应长度:`, responseText.length);

          let decryptedData = null;
          let error = null;

          if (responseText.trim() && /^[0-9a-fA-F]+$/.test(responseText.trim())) {
            try {
              const encryptedData = hexToUint8Array(responseText.trim());
              const decrypted = await aesDecrypt(encryptedData, key, iv);
              const decryptedText = new TextDecoder().decode(decrypted);
              decryptedData = JSON.parse(decryptedText);
            } catch (decryptError) {
              error = `解密失败: ${decryptError.message}`;
            }
          } else {
            error = `无效响应格式: ${responseText.substring(0, 100)}`;
          }

          results.push({
            config: config.name,
            status: response.status,
            responseLength: responseText.length,
            responsePreview: responseText.substring(0, 100),
            decryptedData: decryptedData,
            error: error
          });

        } catch (testError) {
          results.push({
            config: config.name,
            error: `请求失败: ${testError.message}`
          });
        }
      }

      return new Response(JSON.stringify({
        success: true,
        message: "认证测试完成",
        results: results,
        timestamp: new Date().toISOString()
      }, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });

    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }, null, 2), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};
