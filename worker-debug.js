// 调试版本的Cloudflare Worker
export default {
  async fetch(request, env, ctx) {
    // 支持CORS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });
    }

    try {
      // API配置
      const apiUrl = 'http://api.skrapp.net/api/serverlist';
      const headers = {
        'accept': '*/*',
        'accept-language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
        'appversion': '1.3.1',
        'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
        'content-type': 'application/x-www-form-urlencoded',
        'Cookie': 'PHPSESSID=fnffo1ivhvt0ouo6ebqn86a0d4'
      };
      const postData = 'data=4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed';

      console.log('开始请求API...');
      
      // 发送POST请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: postData
      });

      console.log('响应状态:', response.status);
      console.log('响应头:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      
      // 返回调试信息
      const debugInfo = {
        success: true,
        timestamp: new Date().toISOString(),
        debug: {
          responseStatus: response.status,
          responseHeaders: Object.fromEntries(response.headers.entries()),
          responseLength: responseText.length,
          responsePreview: responseText.substring(0, 200),
          isHex: /^[0-9a-fA-F]+$/.test(responseText.trim()),
          fullResponse: responseText
        }
      };

      return new Response(JSON.stringify(debugInfo, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });

    } catch (error) {
      console.error('错误:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }, null, 2), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};
