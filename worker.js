// Cloudflare Worker版本的SS节点提取器
// 原Python脚本转换为JavaScript

export default {
  async fetch(request) {
    // 支持CORS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });
    }
    try {
      // 打印欢迎信息
      console.log("      H͜͡E͜͡L͜͡L͜͡O͜͡ ͜͡W͜͡O͜͡R͜͡L͜͡D͜͡ ͜͡E͜͡X͜͡T͜͡R͜͡A͜͡C͜͡T͜͡ ͜͡S͜͡S͜͡ ͜͡N͜͡O͜͡D͜͡E͜͡");
      console.log("𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟");
      console.log("Author : 𝐼𝑢");
      console.log(`Date   : ${new Date().toISOString().split('T')[0]}`);
      console.log("Version: 1.0 (Cloudflare Worker)");
      console.log("𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟");

      // API配置
      const apiUrl = 'http://api.skrapp.net/api/serverlist';
      const headers = {
        'accept': '*/*',
        'accept-language': 'zh-<PERSON>-CN;q=1, en-CN;q=0.9',
        'appversion': '1.3.1',
        'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
        'content-type': 'application/x-www-form-urlencoded',
        'Cookie': 'PHPSESSID=fnffo1ivhvt0ouo6ebqn86a0d4'
      };
      const postData = 'data=4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed';

      // 十六进制字符串转Uint8Array
      function hexToUint8Array(hexString) {
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
          bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
        }
        return bytes;
      }

      // AES解密密钥和IV (从原Python脚本转换)
      // 原始: d = b'65151f8d966bf596', e = b'88ca0f0ea1ecf975'
      // 这些是ASCII字符串的字节表示，不是十六进制数据
      const key = new TextEncoder().encode('65151f8d966bf596');
      const iv = new TextEncoder().encode('88ca0f0ea1ecf975');

      // AES-CBC解密函数
      async function aesDecrypt(encryptedData, key, iv) {
        try {
          console.log('开始AES解密...');
          console.log('密钥长度:', key.length, '字节');
          console.log('IV长度:', iv.length, '字节');
          console.log('加密数据长度:', encryptedData.length, '字节');

          const cryptoKey = await crypto.subtle.importKey(
            'raw',
            key,
            { name: 'AES-CBC' },
            false,
            ['decrypt']
          );

          const decrypted = await crypto.subtle.decrypt(
            { name: 'AES-CBC', iv: iv },
            cryptoKey,
            encryptedData
          );

          console.log('解密成功，原始长度:', decrypted.byteLength);

          // 移除PKCS7填充
          const decryptedArray = new Uint8Array(decrypted);
          if (decryptedArray.length === 0) {
            throw new Error('解密结果为空');
          }

          const paddingLength = decryptedArray[decryptedArray.length - 1];
          console.log('填充长度:', paddingLength);

          if (paddingLength > 16 || paddingLength > decryptedArray.length) {
            console.warn('异常的填充长度，可能解密失败');
            return decryptedArray; // 返回原始数据，不移除填充
          }

          const result = decryptedArray.slice(0, decryptedArray.length - paddingLength);
          console.log('移除填充后长度:', result.length);
          return result;
        } catch (error) {
          console.error('AES解密失败:', error);
          throw error;
        }
      }



      // Base64编码函数
      function base64Encode(str) {
        // 使用TextEncoder替代已弃用的unescape
        const encoder = new TextEncoder();
        const data = encoder.encode(str);
        return btoa(String.fromCharCode(...data));
      }

      // 发送POST请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: postData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log('API响应长度:', responseText.length);
      console.log('API响应前100字符:', responseText.substring(0, 100));

      // 验证响应是否为有效的十六进制字符串
      if (!responseText.trim() || !/^[0-9a-fA-F]+$/.test(responseText.trim())) {
        throw new Error(`无效的API响应格式: ${responseText.substring(0, 200)}`);
      }

      // 解密数据
      const encryptedHex = responseText.trim();
      console.log('加密数据长度:', encryptedHex.length);

      const encryptedData = hexToUint8Array(encryptedHex);
      console.log('加密字节数组长度:', encryptedData.length);

      const decryptedData = await aesDecrypt(encryptedData, key, iv);
      console.log('解密数据长度:', decryptedData.length);

      // 转换为字符串并解析JSON
      const decryptedText = new TextDecoder().decode(decryptedData);
      console.log('解密文本前200字符:', decryptedText.substring(0, 200));

      // 验证解密后的文本是否为有效JSON
      if (!decryptedText.trim()) {
        throw new Error('解密后的数据为空');
      }

      let jsonData;
      try {
        jsonData = JSON.parse(decryptedText);
      } catch (parseError) {
        console.error('JSON解析错误:', parseError);
        console.error('解密文本:', decryptedText);
        throw new Error(`JSON解析失败: ${parseError.message}. 解密文本: ${decryptedText.substring(0, 500)}`);
      }

      // 验证JSON数据结构
      if (!jsonData || typeof jsonData !== 'object') {
        throw new Error(`无效的JSON数据结构: ${typeof jsonData}`);
      }

      console.log('解密后的JSON数据:', JSON.stringify(jsonData, null, 2));

      // 检查是否是请求信息而不是服务器列表
      if (jsonData.request_ip && jsonData.request_type) {
        return new Response(JSON.stringify({
          success: false,
          error: '解密成功，但返回的是请求信息而不是服务器列表',
          decryptedData: jsonData,
          timestamp: new Date().toISOString()
        }, null, 2), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        });
      }

      if (!jsonData.data || !Array.isArray(jsonData.data)) {
        throw new Error(`JSON数据中缺少data数组: ${JSON.stringify(jsonData)}`);
      }

      console.log('找到服务器数量:', jsonData.data.length);

      // 生成SS链接
      const ssLinks = [];
      for (const server of jsonData.data) {
        if (!server.password || !server.ip || !server.port || !server.title) {
          console.warn('跳过无效服务器数据:', server);
          continue;
        }

        const ssConfig = `aes-256-cfb:${server.password}@${server.ip}:${server.port}`;
        const encodedConfig = base64Encode(ssConfig);
        const ssLink = `ss://${encodedConfig}#${server.title}`;
        ssLinks.push(ssLink);
        console.log('生成SS链接:', ssLink);
      }

      // 返回结果
      const result = {
        success: true,
        timestamp: new Date().toISOString(),
        count: ssLinks.length,
        links: ssLinks
      };

      return new Response(JSON.stringify(result, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });

    } catch (error) {
      console.error('错误:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};
