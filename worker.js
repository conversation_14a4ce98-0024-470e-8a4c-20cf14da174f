// Cloudflare Worker版本的SS节点提取器
// 原Python脚本转换为JavaScript

export default {
  async fetch(request, env, ctx) {
    try {
      // 打印欢迎信息
      console.log("      H͜͡E͜͡L͜͡L͜͡O͜͡ ͜͡W͜͡O͜͡R͜͡L͜͡D͜͡ ͜͡E͜͡X͜͡T͜͡R͜͡A͜͡C͜͡T͜͡ ͜͡S͜͡S͜͡ ͜͡N͜͡O͜͡D͜͡E͜͡");
      console.log("𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟");
      console.log("Author : 𝐼𝑢");
      console.log(`Date   : ${new Date().toISOString().split('T')[0]}`);
      console.log("Version: 1.0 (Cloudflare Worker)");
      console.log("𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟 𓆝 𓆟 𓆞 𓆟");

      // API配置
      const apiUrl = 'http://api.skrapp.net/api/serverlist';
      const headers = {
        'accept': '*/*',
        'accept-language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
        'appversion': '1.3.1',
        'user-agent': 'SkrKK/1.3.1 (iPhone; iOS 13.5; Scale/2.00)',
        'content-type': 'application/x-www-form-urlencoded',
        'Cookie': 'PHPSESSID=fnffo1ivhvt0ouo6ebqn86a0d4'
      };
      const postData = 'data=4265a9c353cd8624fd2bc7b5d75d2f18b1b5e66ccd37e2dfa628bcb8f73db2f14ba98bc6a1d8d0d1c7ff1ef0823b11264d0addaba2bd6a30bdefe06f4ba994ed';
      
      // AES解密密钥和IV
      const key = new Uint8Array([0x36, 0x35, 0x31, 0x35, 0x31, 0x66, 0x38, 0x64, 0x39, 0x36, 0x36, 0x62, 0x66, 0x35, 0x39, 0x36]);
      const iv = new Uint8Array([0x38, 0x38, 0x63, 0x61, 0x30, 0x66, 0x30, 0x65, 0x61, 0x31, 0x65, 0x63, 0x66, 0x39, 0x37, 0x35]);

      // AES-CBC解密函数
      async function aesDecrypt(encryptedData, key, iv) {
        const cryptoKey = await crypto.subtle.importKey(
          'raw',
          key,
          { name: 'AES-CBC' },
          false,
          ['decrypt']
        );
        
        const decrypted = await crypto.subtle.decrypt(
          { name: 'AES-CBC', iv: iv },
          cryptoKey,
          encryptedData
        );
        
        // 移除PKCS7填充
        const decryptedArray = new Uint8Array(decrypted);
        const paddingLength = decryptedArray[decryptedArray.length - 1];
        return decryptedArray.slice(0, decryptedArray.length - paddingLength);
      }

      // 十六进制字符串转Uint8Array
      function hexToUint8Array(hexString) {
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
          bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
        }
        return bytes;
      }

      // Base64编码函数
      function base64Encode(str) {
        return btoa(unescape(encodeURIComponent(str)));
      }

      // 发送POST请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: postData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log('API响应:', responseText);

      // 解密数据
      const encryptedHex = responseText.trim();
      const encryptedData = hexToUint8Array(encryptedHex);
      const decryptedData = await aesDecrypt(encryptedData, key, iv);
      
      // 转换为字符串并解析JSON
      const decryptedText = new TextDecoder().decode(decryptedData);
      const jsonData = JSON.parse(decryptedText);

      // 生成SS链接
      const ssLinks = [];
      for (const server of jsonData.data) {
        const ssConfig = `aes-256-cfb:${server.password}@${server.ip}:${server.port}`;
        const encodedConfig = base64Encode(ssConfig);
        const ssLink = `ss://${encodedConfig}#${server.title}`;
        ssLinks.push(ssLink);
      }

      // 返回结果
      const result = {
        success: true,
        timestamp: new Date().toISOString(),
        count: ssLinks.length,
        links: ssLinks
      };

      return new Response(JSON.stringify(result, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      });

    } catch (error) {
      console.error('错误:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};
